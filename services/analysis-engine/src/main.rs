use anyhow::Result;
use axum::{
    Router,
    routing::{get, post, delete},
};
use std::net::SocketAddr;
use std::env;
use tower_http::{
    cors::Cors<PERSON>ayer,
    trace::TraceLayer,
    compression::CompressionLayer,
};
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub mod config;
mod api;
mod auth;
mod audit;
mod models;
mod services;
mod metrics;
mod storage;
mod git;
mod parser;

#[tokio::main]
async fn main() -> Result<()> {
    // Load environment variables from .env file
    dotenv::dotenv().ok();

    // Initialize tracing
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "analysis_engine=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // Cloud Run sets the PORT environment variable - read it before config initialization
    let port = env::var("PORT")
        .unwrap_or_else(|_| "8001".to_string())
        .parse::<u16>()
        .unwrap_or(8001);
    
    info!("PORT environment variable: {}", port);

    let config = config::ServiceConfig::from_env()?;

    info!("Starting Analysis Engine service on port {}", port);

    // Build the application
    let app = create_app().await.map_err(|e| {
        tracing::error!("Failed to create application: {:?}", e);
        e
    })?;

    // Run the server on the port specified in the configuration
    let addr = SocketAddr::from(([0, 0, 0, 0], port));
    info!("Analysis Engine listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

async fn create_app() -> Result<Router> {
    // Initialize services
    let state = api::AppState::new().await?;

    // Create protected routes that require authentication
    let protected_routes = Router::new()
        // Core analysis endpoints
        .route("/api/v1/analysis", post(api::handlers::create_analysis))
        .route("/api/v1/analysis", get(api::handlers::list_analyses))
        .route("/api/v1/analysis/{id}", get(api::handlers::get_analysis))
        .route("/api/v1/analysis/{id}/results", get(api::handlers::get_analysis_results))
        .route("/api/v1/analysis/{id}", delete(api::handlers::cancel_analysis))
        .route("/api/v1/analysis/{id}/status", get(api::handlers::get_analysis_status))
        .route("/api/v1/analysis/{id}/download", get(api::handlers::download_analysis))
        .route("/api/v1/analysis/{id}/metrics", get(api::handlers::get_analysis_metrics))
        .route("/api/v1/analysis/{id}/patterns", get(api::handlers::get_analysis_patterns))
        .route("/api/v1/analysis/{id}/warnings", get(api::handlers::get_analysis_warnings))

        // WebSocket endpoint for real-time progress
        .route("/ws/analysis/{id}", get(api::handlers::websocket_handler))

        // Languages endpoint
        .route("/api/v1/languages", get(api::handlers::supported_languages))

        // TODO: Apply authentication middleware to protected routes
        // .layer(axum::middleware::from_fn_with_state(state.clone(), api::middleware::auth_middleware))
        ;

    // Create public routes that don't require authentication
    let public_routes = Router::new()
        // Health and monitoring endpoints
        .route("/health", get(api::handlers::health))
        .route("/health/auth", get(api::handlers::auth_status))
        .route("/ready", get(api::handlers::ready))
        .route("/metrics", get(api::handlers::metrics));

    // Build the main application by merging routes
    let app = Router::new()
        .merge(protected_routes)
        .merge(public_routes)
        // Add state
        .with_state(state)
        // Add global middleware layers (order matters - last added runs first)
        .layer(axum::middleware::from_fn(api::middleware::request_id_middleware))
        .layer(axum::middleware::from_fn(api::middleware::security_headers_middleware))
        .layer(axum::middleware::from_fn(api::middleware::api_security_headers_middleware))
        .layer(CorsLayer::permissive())
        .layer(CompressionLayer::new())
        .layer(TraceLayer::new_for_http());

    Ok(app)
}
