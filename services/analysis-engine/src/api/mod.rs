pub mod handlers;
pub mod websocket;
pub mod middleware;
pub mod errors;

use std::sync::Arc;
use dashmap::DashMap;
use tokio::sync::{broadcast, Semaphore};
use crate::storage::{gcp_clients, SpannerOperations, StorageOperations, PubSubOperations, RedisClient, CacheManager};
use crate::config::ServiceConfig;
use crate::services::analyzer::AnalysisService;
use crate::models::{AnalysisStatus, ProgressUpdate};
use crate::backpressure::BackpressureManager;

#[derive(Clone)]
pub struct AppState {
    pub spanner: Option<Arc<SpannerOperations>>,
    pub storage: Arc<StorageOperations>,
    pub pubsub: Arc<PubSubOperations>,
    pub redis: Option<Arc<RedisClient>>,
    pub cache: Arc<CacheManager>,
    pub config: Arc<ServiceConfig>,
    pub analysis_service: Arc<AnalysisService>,
    pub active_analyses: Arc<DashMap<String, AnalysisStatus>>,
    pub progress_broadcast: broadcast::Sender<ProgressUpdate>,
    pub backpressure_manager: Arc<BackpressureManager>,
}

impl AppState {
    pub async fn new() -> Result<Self, anyhow::Error> {
        let config = Arc::new(ServiceConfig::from_env()?);
        
        // Create the actual GCP clients with proper configuration
        // Make Spanner optional for deployment
        let spanner = match gcp_clients::create_spanner_client(&config.gcp).await {
            Ok(client) => {
                match SpannerOperations::new(
                    client,
                    config.gcp.project_id.clone(),
                    config.gcp.spanner_instance.clone(),
                    config.gcp.spanner_database.clone(),
                ).await {
                    Ok(ops) => {
                        tracing::info!("Spanner client initialized successfully");
                        Some(Arc::new(ops))
                    }
                    Err(e) => {
                        tracing::warn!("Failed to initialize Spanner operations: {}. Running without database.", e);
                        None
                    }
                }
            }
            Err(e) => {
                tracing::warn!("Failed to create Spanner client: {}. Running without database.", e);
                None
            }
        };
        
        let storage_client = gcp_clients::create_storage_client(&config.gcp).await?;
        let pubsub_client = gcp_clients::create_pubsub_client(&config.gcp).await?;
        
        // Create operation wrappers
        let storage = Arc::new(StorageOperations::new(storage_client).await?);
        let pubsub = Arc::new(PubSubOperations::new(pubsub_client).await?);
        
        // Create Redis client (optional, for rate limiting and caching)
        let redis = match RedisClient::new().await {
            Ok(client) => {
                tracing::info!("Redis client initialized successfully");
                Some(Arc::new(client))
            }
            Err(e) => {
                tracing::warn!("Failed to initialize Redis client: {}. Rate limiting will use in-memory fallback.", e);
                None
            }
        };
        
        // Create cache manager with Redis client
        let cache = Arc::new(CacheManager::new(redis.clone()));
        
        // Create broadcast channel for progress updates
        // Buffer size of 1000 should handle high-frequency updates
        let (progress_broadcast, _) = broadcast::channel(1000);
        
        // Create backpressure manager
        let backpressure_config = crate::backpressure::BackpressureConfig {
            max_concurrent_analyses: config.analysis.max_concurrent_analyses,
            max_concurrent_parsing: config.analysis.max_concurrent_analyses * 4, // 4x for file parsing
            max_concurrent_database: 100,
            max_concurrent_storage: 150,
            memory_threshold_mb: 3000,
            cpu_threshold_percent: 80.0,
            queue_size_threshold: 1000,
            circuit_breaker_failure_threshold: 5,
            circuit_breaker_timeout: std::time::Duration::from_secs(30),
        };
        let backpressure_manager = Arc::new(BackpressureManager::new(backpressure_config));

        // Create analysis service with the operation wrappers
        let mut analysis_service = AnalysisService::new(
            spanner.clone(),
            storage.clone(),
            pubsub.clone(),
            cache.clone(),
            config.clone(),
        ).await?;

        // Set the backpressure manager on the analysis service
        analysis_service.set_backpressure_manager(backpressure_manager.clone());
        let analysis_service = Arc::new(analysis_service);

        Ok(Self {
            spanner,
            storage,
            pubsub,
            redis,
            cache,
            config,
            analysis_service,
            active_analyses: Arc::new(DashMap::new()),
            progress_broadcast,
            backpressure_manager,
        })
    }
}
