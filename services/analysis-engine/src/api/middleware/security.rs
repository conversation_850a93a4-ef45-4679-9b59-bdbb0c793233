use axum::{
    extract::Request,
    http::header,
    middleware::Next,
    response::Response,
};

/// Security headers middleware that adds comprehensive security headers to all responses
/// 
/// This middleware implements defense-in-depth security by adding multiple layers of protection:
/// - Content Security Policy (CSP) to prevent XSS attacks
/// - HTTP Strict Transport Security (HSTS) to enforce HTTPS
/// - X-Frame-Options to prevent clickjacking
/// - X-Content-Type-Options to prevent MIME type sniffing
/// - X-XSS-Protection for legacy browser protection
/// - Referrer-Policy to control referrer information leakage
/// - Permissions-Policy to restrict access to browser features
/// - Cache-Control headers for sensitive endpoints
pub async fn security_headers_middleware(
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;
    let headers = response.headers_mut();

    // HTTP Strict Transport Security (HSTS)
    // Enforces HTTPS for 1 year, includes subdomains, and enables preload
    headers.insert(
        header::STRICT_TRANSPORT_SECURITY,
        "max-age=31536000; includeSubDomains; preload".parse().unwrap(),
    );

    // Content Security Policy (CSP)
    // Restrictive policy that only allows same-origin resources
    let csp_policy = [
        "default-src 'self'",
        "script-src 'self'",
        "style-src 'self' 'unsafe-inline'", // Allow inline styles for error pages
        "img-src 'self' data:",
        "font-src 'self'",
        "connect-src 'self'",
        "frame-src 'none'",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "upgrade-insecure-requests",
    ].join("; ");
    
    headers.insert(
        header::CONTENT_SECURITY_POLICY,
        csp_policy.parse().unwrap(),
    );

    // X-Frame-Options: Prevent clickjacking attacks
    headers.insert(
        axum::http::HeaderName::from_static("x-frame-options"),
        "DENY".parse().unwrap(),
    );

    // X-Content-Type-Options: Prevent MIME type sniffing
    headers.insert(
        axum::http::HeaderName::from_static("x-content-type-options"),
        "nosniff".parse().unwrap(),
    );

    // X-XSS-Protection: Enable XSS filtering (legacy browsers)
    headers.insert(
        axum::http::HeaderName::from_static("x-xss-protection"),
        "1; mode=block".parse().unwrap(),
    );

    // Referrer-Policy: Control referrer information leakage
    headers.insert(
        axum::http::HeaderName::from_static("referrer-policy"),
        "strict-origin-when-cross-origin".parse().unwrap(),
    );

    // Permissions-Policy: Restrict access to browser features
    let permissions_policy = [
        "geolocation=()",
        "microphone=()",
        "camera=()",
        "payment=()",
        "usb=()",
        "magnetometer=()",
        "gyroscope=()",
        "speaker=()",
        "vibrate=()",
        "fullscreen=(self)",
    ].join(", ");

    headers.insert(
        axum::http::HeaderName::from_static("permissions-policy"),
        permissions_policy.parse().unwrap(),
    );

    // Cross-Origin-Embedder-Policy: Require CORP for cross-origin resources
    headers.insert(
        axum::http::HeaderName::from_static("cross-origin-embedder-policy"),
        "require-corp".parse().unwrap(),
    );

    // Cross-Origin-Opener-Policy: Isolate browsing context
    headers.insert(
        axum::http::HeaderName::from_static("cross-origin-opener-policy"),
        "same-origin".parse().unwrap(),
    );

    // Cross-Origin-Resource-Policy: Control cross-origin resource sharing
    headers.insert(
        axum::http::HeaderName::from_static("cross-origin-resource-policy"),
        "same-origin".parse().unwrap(),
    );

    // Server header removal (don't advertise server technology)
    headers.remove(header::SERVER);

    // X-Powered-By header removal (if present)
    headers.remove(axum::http::HeaderName::from_static("x-powered-by"));

    response
}

/// Additional security headers for API endpoints that handle sensitive data
pub async fn api_security_headers_middleware(
    request: Request,
    next: Next,
) -> Response {
    let mut response = next.run(request).await;
    let headers = response.headers_mut();

    // Cache-Control: Prevent caching of sensitive API responses
    headers.insert(
        header::CACHE_CONTROL,
        "no-store, no-cache, must-revalidate, private".parse().unwrap(),
    );

    // Pragma: Additional cache control for HTTP/1.0 compatibility
    headers.insert(
        header::PRAGMA,
        "no-cache".parse().unwrap(),
    );

    // Expires: Set expiration date in the past
    headers.insert(
        header::EXPIRES,
        "0".parse().unwrap(),
    );

    response
}
