"""
LLM Service V2 - Using Google GenAI SDK
Migration from deprecated Vertex AI SDK to unified google-genai SDK
July 2025
"""

import asyncio
import json
import os
import time
from typing import List, Dict, Any, Optional

import structlog
from google import genai
from google.genai.types import GenerateContentConfig
import google.auth
from google.oauth2 import service_account

from ..models import (
    QueryContext,
    IntentAnalysis,
    CodeChunk,
    GeneratedResponse,
)
from ..config.settings import get_settings

logger = structlog.get_logger()
settings = get_settings()


class LLMServiceV2:
    """Service for LLM interactions using Google GenAI SDK"""

    def __init__(self):
        """Initialize the LLM service with appropriate backend"""
        try:
            # Determine which backend to use
            self.use_vertex_ai = settings.USE_VERTEX_AI or os.getenv("USE_VERTEX_AI", "true").lower() == "true"
            
            if self.use_vertex_ai:
                self._init_vertex_ai()
            else:
                self._init_gemini_api()
            
            # Initialize model with fallback strategy
            self._init_model()
            
            # Configure generation settings
            self._init_generation_configs()
            
            logger.info(
                "llm_service_initialized",
                backend="Vertex AI" if self.use_vertex_ai else "Gemini API",
                model=self.model_name
            )
            
        except Exception as e:
            logger.error("llm_service_init_failed", error=str(e), exc_info=True)
            raise

    def _init_vertex_ai(self):
        """Initialize Vertex AI backend"""
        # Handle authentication
        if settings.SERVICE_ACCOUNT_PATH:
            # Use service account
            credentials = service_account.Credentials.from_service_account_file(
                settings.SERVICE_ACCOUNT_PATH,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )
            project_id = settings.GCP_PROJECT_ID
        else:
            # Use Application Default Credentials
            credentials, project_id = google.auth.default()
        
        # Create client for Vertex AI
        self.client = genai.Client(
            vertexai=True,
            project=settings.GCP_PROJECT_ID or project_id,
            location=settings.GCP_REGION or "us-central1",
            credentials=credentials
        )
        
        logger.info("vertex_ai_configured", project=project_id, location=settings.GCP_REGION)

    def _init_gemini_api(self):
        """Initialize Gemini Developer API backend"""
        # Get API key
        api_key = settings.GOOGLE_API_KEY or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY required for Gemini API backend")
        
        # Create client for Gemini API
        self.client = genai.Client(api_key=api_key)
        logger.info("gemini_api_configured")

    def _init_model(self):
        """Initialize model with fallback strategy"""
        # Model preference order based on July 2025 recommendations
        model_candidates = [
            settings.GEMINI_MODEL_NAME or "gemini-2.5-flash",
            "gemini-2.5-flash",
            "gemini-2.0-flash-exp",
            "gemini-1.5-pro",
            "gemini-1.5-flash"
        ]
        
        for model_name in model_candidates:
            try:
                # Test the model with a simple prompt
                test_response = self.client.models.generate_content(
                    model=model_name,
                    contents="Hello"
                )
                if test_response.text:
                    self.model_name = model_name
                    logger.info("model_initialized", model=model_name)
                    return
            except Exception as e:
                logger.warning("model_init_failed", model=model_name, error=str(e))
                continue
        
        raise RuntimeError(f"Failed to initialize any model from candidates: {model_candidates}")

    def _init_generation_configs(self):
        """Initialize generation configurations"""
        # Default configuration for responses
        self.default_config = GenerateContentConfig(
            temperature=0.3,
            max_output_tokens=2048,
            top_p=0.95,
            top_k=40,
        )
        
        # JSON configuration for structured responses
        self.json_config = GenerateContentConfig(
            temperature=0.1,
            max_output_tokens=1024,
            response_mime_type="application/json",
        )
        
        # Fast configuration for simple queries
        self.fast_config = GenerateContentConfig(
            temperature=0.1,
            max_output_tokens=512,
            top_p=0.8,
            top_k=10,
        )

    async def generate_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
    ) -> GeneratedResponse:
        """Generate response based on query and code context"""
        start_time = time.time()

        logger.info(
            "llm_response_generation_start",
            query_length=len(query),
            chunk_count=len(code_chunks),
            intent=intent.primary_intent.value,
        )

        try:
            # Build prompt
            prompt = self._build_response_prompt(query, intent, code_chunks, context)

            # Choose configuration based on intent
            config = self._select_config(intent)

            # Generate response asynchronously
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=prompt,
                config=config,
            )

            generation_time_ms = (time.time() - start_time) * 1000

            # Calculate confidence
            confidence = self._calculate_confidence(response, code_chunks, intent)

            # Extract usage metadata if available
            prompt_tokens = None
            completion_tokens = None
            if hasattr(response, 'usage_metadata'):
                usage = response.usage_metadata
                if hasattr(usage, 'prompt_token_count'):
                    prompt_tokens = usage.prompt_token_count
                if hasattr(usage, 'candidates_token_count'):
                    completion_tokens = usage.candidates_token_count
            
            result = GeneratedResponse(
                text=response.text,
                confidence=confidence,
                model_used=self.model_name,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                generation_time_ms=generation_time_ms,
            )

            logger.info(
                "llm_response_generated",
                generation_time_ms=generation_time_ms,
                response_length=len(response.text),
                confidence=confidence,
            )

            return result

        except Exception as e:
            logger.error("llm_response_generation_failed", error=str(e), exc_info=True)
            raise

    async def generate_json_response(self, prompt: str) -> Dict[str, Any]:
        """Generate a JSON response from the LLM"""
        try:
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=prompt,
                config=self.json_config,
            )

            # Parse JSON response
            try:
                return json.loads(response.text)
            except json.JSONDecodeError:
                # Clean up response if needed
                text = response.text.strip()
                if text.startswith("```json"):
                    text = text[7:]
                if text.endswith("```"):
                    text = text[:-3]
                return json.loads(text.strip())

        except Exception as e:
            logger.error("llm_json_generation_failed", error=str(e), exc_info=True)
            # Return a default structure based on common patterns
            return self._get_default_json_response()

    async def stream_response(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
    ):
        """Stream response generation for real-time output"""
        prompt = self._build_response_prompt(query, intent, code_chunks, context)
        config = self._select_config(intent)

        try:
            # Note: The new SDK might have different streaming API
            # For now, using non-streaming as fallback
            response = await asyncio.to_thread(
                self.client.models.generate_content,
                model=self.model_name,
                contents=prompt,
                config=config,
            )

            # Yield the response text (streaming might need different implementation)
            if response.text:
                yield response.text

        except Exception as e:
            logger.error("streaming_failed", error=str(e))
            yield f"\n\nError generating response: {str(e)}"

    def _select_config(self, intent: IntentAnalysis) -> GenerateContentConfig:
        """Select appropriate generation config based on intent"""
        if intent.primary_intent.value in ["find", "debug"]:
            # Use fast config for simple lookups
            return self.fast_config
        elif intent.primary_intent.value in ["explain", "analyze", "compare"]:
            # Use default config for complex responses
            return self.default_config
        else:
            return self.default_config

    def _build_response_prompt(
        self,
        query: str,
        intent: IntentAnalysis,
        code_chunks: List[CodeChunk],
        context: QueryContext,
    ) -> str:
        """Build prompt for response generation"""
        # Format code context
        code_context = self._format_code_context(code_chunks)

        # Format conversation history
        history_context = self._format_history(context.history[-3:])

        # Build intent-specific instructions
        intent_instructions = self._get_intent_instructions(intent)

        prompt = f"""You are an AI assistant helping developers understand their codebase.
You have deep knowledge of software development, design patterns, and best practices.

User Query: {query}
Query Intent: {intent.primary_intent.value}
Code Elements of Interest: {', '.join(intent.code_elements) if intent.code_elements else 'General'}

{intent_instructions}

Relevant Code Context:
{code_context}

{history_context}

Instructions for your response:
1. Directly address the user's query with accurate, helpful information
2. Reference specific code sections when relevant using the format: `filename:line_number`
3. Explain technical concepts clearly and concisely
4. If suggesting improvements or alternatives, explain why they would be beneficial
5. Be honest if the provided context doesn't fully answer the question
6. Keep your response focused and well-structured

Remember to:
- Use code blocks with language syntax highlighting
- Provide concrete examples when helpful
- Suggest logical next steps or related areas to explore
- Maintain a professional, helpful tone"""

        return prompt

    def _format_code_context(self, chunks: List[CodeChunk]) -> str:
        """Format code chunks for inclusion in prompt"""
        if not chunks:
            return "No relevant code context found."

        context_parts = []
        for i, chunk in enumerate(chunks[:5], 1):  # Limit to top 5 chunks
            context_parts.append(
                f"""
--- Code Section {i} ---
File: {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line})
Language: {chunk.language}
Relevance Score: {chunk.combined_score:.2f}

```{chunk.language}
{chunk.content}
```"""
            )

        return "\n".join(context_parts)

    def _format_history(self, history: List[Dict[str, Any]]) -> str:
        """Format conversation history"""
        if not history:
            return ""

        history_parts = ["Previous Conversation Context:"]
        for item in history:
            if item.get("query"):
                history_parts.append(f"User: {item['query']}")
            if item.get("answer"):
                history_parts.append(f"Assistant: {item['answer'][:200]}...")

        return "\n".join(history_parts) if len(history_parts) > 1 else ""

    def _get_intent_instructions(self, intent: IntentAnalysis) -> str:
        """Get intent-specific instructions"""
        instructions_map = {
            "explain": """Focus on providing a clear, educational explanation.
Break down complex concepts into understandable parts.
Use analogies or diagrams if helpful.""",
            
            "find": """Help locate the specific code or functionality requested.
Provide exact file paths and line numbers.
Mention related code that might also be relevant.""",
            
            "debug": """Analyze the code for potential issues or bugs.
Suggest specific fixes or debugging approaches.
Consider edge cases and error conditions.""",
            
            "refactor": """Suggest improvements to code structure and design.
Explain the benefits of proposed changes.
Consider maintainability and best practices.""",
            
            "analyze": """Provide a comprehensive analysis of the code.
Discuss patterns, architecture, and design decisions.
Identify strengths and potential improvements.""",
            
            "compare": """Compare and contrast different approaches or implementations.
Highlight trade-offs and use cases for each option.
Provide recommendations based on the context.""",
        }

        return instructions_map.get(
            intent.primary_intent.value,
            "Provide a helpful, accurate response to the query.",
        )

    def _calculate_confidence(
        self, response: Any, chunks: List[CodeChunk], intent: IntentAnalysis
    ) -> float:
        """Calculate confidence score for the response"""
        base_confidence = 0.7

        # Boost for high-quality code chunks
        if chunks:
            avg_chunk_score = sum(c.combined_score for c in chunks[:3]) / min(3, len(chunks))
            base_confidence += 0.15 * avg_chunk_score

        # Boost for high intent confidence
        base_confidence += 0.1 * intent.confidence

        # Adjust based on response characteristics
        if response and hasattr(response, "text") and response.text:
            # Penalize very short responses
            if len(response.text) < 100:
                base_confidence *= 0.8
            # Penalize responses that seem uncertain
            uncertainty_phrases = ["i'm not sure", "might be", "possibly", "unclear"]
            if any(phrase in response.text.lower() for phrase in uncertainty_phrases):
                base_confidence *= 0.9

        # Cap confidence at 0.95
        return min(base_confidence, 0.95)

    def _get_default_json_response(self) -> Dict[str, Any]:
        """Get default JSON response structure"""
        return {
            "primary_intent": "unknown",
            "code_elements": [],
            "scope": "repository",
            "context_depth": "normal",
            "confidence": 0.5
        }

    async def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        return {
            "model_name": self.model_name,
            "backend": "Vertex AI" if self.use_vertex_ai else "Gemini API",
            "max_output_tokens": self.default_config.max_output_tokens,
            "temperature": self.default_config.temperature,
        }


def get_llm_service() -> LLMServiceV2:
    """Factory function to get LLM service instance"""
    return LLMServiceV2()